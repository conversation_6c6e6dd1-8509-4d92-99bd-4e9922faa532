<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="198" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="ignoredPackages">
        <value>
          <list size="168">
            <item index="0" class="java.lang.String" itemvalue="psycopg2" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="mailchimp-transactional" />
            <item index="3" class="java.lang.String" itemvalue="Automat" />
            <item index="4" class="java.lang.String" itemvalue="greenlet" />
            <item index="5" class="java.lang.String" itemvalue="googleapis-common-protos" />
            <item index="6" class="java.lang.String" itemvalue="PyYAML" />
            <item index="7" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="8" class="java.lang.String" itemvalue="drf-spectacular-sidecar" />
            <item index="9" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="10" class="java.lang.String" itemvalue="pycparser" />
            <item index="11" class="java.lang.String" itemvalue="frozenlist" />
            <item index="12" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="13" class="java.lang.String" itemvalue="drf-spectacular" />
            <item index="14" class="java.lang.String" itemvalue="djangorestframework-simplejwt" />
            <item index="15" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="16" class="java.lang.String" itemvalue="certifi" />
            <item index="17" class="java.lang.String" itemvalue="stripe" />
            <item index="18" class="java.lang.String" itemvalue="lxml" />
            <item index="19" class="java.lang.String" itemvalue="pyparsing" />
            <item index="20" class="java.lang.String" itemvalue="jsonschema" />
            <item index="21" class="java.lang.String" itemvalue="google-api-core" />
            <item index="22" class="java.lang.String" itemvalue="dnspython" />
            <item index="23" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="24" class="java.lang.String" itemvalue="python-engineio" />
            <item index="25" class="java.lang.String" itemvalue="asgiref" />
            <item index="26" class="java.lang.String" itemvalue="cryptography" />
            <item index="27" class="java.lang.String" itemvalue="zope.interface" />
            <item index="28" class="java.lang.String" itemvalue="gunicorn" />
            <item index="29" class="java.lang.String" itemvalue="wsproto" />
            <item index="30" class="java.lang.String" itemvalue="attrs" />
            <item index="31" class="java.lang.String" itemvalue="crypto" />
            <item index="32" class="java.lang.String" itemvalue="python-socketio" />
            <item index="33" class="java.lang.String" itemvalue="boto3" />
            <item index="34" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="35" class="java.lang.String" itemvalue="timezonefinder" />
            <item index="36" class="java.lang.String" itemvalue="shellescape" />
            <item index="37" class="java.lang.String" itemvalue="referencing" />
            <item index="38" class="java.lang.String" itemvalue="geopy" />
            <item index="39" class="java.lang.String" itemvalue="PyJWT" />
            <item index="40" class="java.lang.String" itemvalue="simple-websocket" />
            <item index="41" class="java.lang.String" itemvalue="async-timeout" />
            <item index="42" class="java.lang.String" itemvalue="qrcode" />
            <item index="43" class="java.lang.String" itemvalue="cffi" />
            <item index="44" class="java.lang.String" itemvalue="django-storages" />
            <item index="45" class="java.lang.String" itemvalue="h3" />
            <item index="46" class="java.lang.String" itemvalue="numpy" />
            <item index="47" class="java.lang.String" itemvalue="pyasn1" />
            <item index="48" class="java.lang.String" itemvalue="requests" />
            <item index="49" class="java.lang.String" itemvalue="bidict" />
            <item index="50" class="java.lang.String" itemvalue="Jinja2" />
            <item index="51" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="52" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="53" class="java.lang.String" itemvalue="eventlet" />
            <item index="54" class="java.lang.String" itemvalue="rpds-py" />
            <item index="55" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="56" class="java.lang.String" itemvalue="zipp" />
            <item index="57" class="java.lang.String" itemvalue="geographiclib" />
            <item index="58" class="java.lang.String" itemvalue="urllib3" />
            <item index="59" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="60" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="61" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="62" class="java.lang.String" itemvalue="Flask" />
            <item index="63" class="java.lang.String" itemvalue="blinker" />
            <item index="64" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="65" class="java.lang.String" itemvalue="pypng" />
            <item index="66" class="java.lang.String" itemvalue="pymongo" />
            <item index="67" class="java.lang.String" itemvalue="botocore" />
            <item index="68" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="69" class="java.lang.String" itemvalue="packaging" />
            <item index="70" class="java.lang.String" itemvalue="hstspreload" />
            <item index="71" class="java.lang.String" itemvalue="incremental" />
            <item index="72" class="java.lang.String" itemvalue="Naked" />
            <item index="73" class="java.lang.String" itemvalue="jmespath" />
            <item index="74" class="java.lang.String" itemvalue="autobahn" />
            <item index="75" class="java.lang.String" itemvalue="s3transfer" />
            <item index="76" class="java.lang.String" itemvalue="Django" />
            <item index="77" class="java.lang.String" itemvalue="libretranslatepy" />
            <item index="78" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="79" class="java.lang.String" itemvalue="cachetools" />
            <item index="80" class="java.lang.String" itemvalue="aiohttp" />
            <item index="81" class="java.lang.String" itemvalue="multidict" />
            <item index="82" class="java.lang.String" itemvalue="google-api-python-client" />
            <item index="83" class="java.lang.String" itemvalue="yarl" />
            <item index="84" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="85" class="java.lang.String" itemvalue="pytz" />
            <item index="86" class="java.lang.String" itemvalue="aiosignal" />
            <item index="87" class="java.lang.String" itemvalue="google-auth" />
            <item index="88" class="java.lang.String" itemvalue="Pillow" />
            <item index="89" class="java.lang.String" itemvalue="Twisted" />
            <item index="90" class="java.lang.String" itemvalue="httpx" />
            <item index="91" class="java.lang.String" itemvalue="rfc3986" />
            <item index="92" class="java.lang.String" itemvalue="click" />
            <item index="93" class="java.lang.String" itemvalue="googletrans" />
            <item index="94" class="java.lang.String" itemvalue="httpcore" />
            <item index="95" class="java.lang.String" itemvalue="idna" />
            <item index="96" class="java.lang.String" itemvalue="rsa" />
            <item index="97" class="java.lang.String" itemvalue="django-environ" />
            <item index="98" class="java.lang.String" itemvalue="sniffio" />
            <item index="99" class="java.lang.String" itemvalue="uritemplate" />
            <item index="100" class="java.lang.String" itemvalue="hyperframe" />
            <item index="101" class="java.lang.String" itemvalue="hpack" />
            <item index="102" class="java.lang.String" itemvalue="inflection" />
            <item index="103" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="104" class="java.lang.String" itemvalue="h11" />
            <item index="105" class="java.lang.String" itemvalue="constantly" />
            <item index="106" class="java.lang.String" itemvalue="oauthlib" />
            <item index="107" class="java.lang.String" itemvalue="daphne" />
            <item index="108" class="java.lang.String" itemvalue="hyperlink" />
            <item index="109" class="java.lang.String" itemvalue="service-identity" />
            <item index="110" class="java.lang.String" itemvalue="channels" />
            <item index="111" class="java.lang.String" itemvalue="django-decouple" />
            <item index="112" class="java.lang.String" itemvalue="httplib2" />
            <item index="113" class="java.lang.String" itemvalue="h2" />
            <item index="114" class="java.lang.String" itemvalue="sqlparse" />
            <item index="115" class="java.lang.String" itemvalue="google-auth-httplib2" />
            <item index="116" class="java.lang.String" itemvalue="txaio" />
            <item index="117" class="java.lang.String" itemvalue="six" />
            <item index="118" class="java.lang.String" itemvalue="chardet" />
            <item index="119" class="java.lang.String" itemvalue="paddleocr" />
            <item index="120" class="java.lang.String" itemvalue="srsly" />
            <item index="121" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="122" class="java.lang.String" itemvalue="paddlepaddle" />
            <item index="123" class="java.lang.String" itemvalue="spacy" />
            <item index="124" class="java.lang.String" itemvalue="Pygments" />
            <item index="125" class="java.lang.String" itemvalue="marisa-trie" />
            <item index="126" class="java.lang.String" itemvalue="pydantic" />
            <item index="127" class="java.lang.String" itemvalue="opentelemetry-api" />
            <item index="128" class="java.lang.String" itemvalue="catalogue" />
            <item index="129" class="java.lang.String" itemvalue="contourpy" />
            <item index="130" class="java.lang.String" itemvalue="pdf2docx" />
            <item index="131" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="132" class="java.lang.String" itemvalue="cymem" />
            <item index="133" class="java.lang.String" itemvalue="pdf2image" />
            <item index="134" class="java.lang.String" itemvalue="matplotlib" />
            <item index="135" class="java.lang.String" itemvalue="murmurhash" />
            <item index="136" class="java.lang.String" itemvalue="shellingham" />
            <item index="137" class="java.lang.String" itemvalue="scikit-image" />
            <item index="138" class="java.lang.String" itemvalue="wasabi" />
            <item index="139" class="java.lang.String" itemvalue="confection" />
            <item index="140" class="java.lang.String" itemvalue="spacy-loggers" />
            <item index="141" class="java.lang.String" itemvalue="opentelemetry-sdk" />
            <item index="142" class="java.lang.String" itemvalue="spacy-legacy" />
            <item index="143" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="144" class="java.lang.String" itemvalue="google-apps-meet" />
            <item index="145" class="java.lang.String" itemvalue="zoomus" />
            <item index="146" class="java.lang.String" itemvalue="preshed" />
            <item index="147" class="java.lang.String" itemvalue="language_data" />
            <item index="148" class="java.lang.String" itemvalue="Deprecated" />
            <item index="149" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="150" class="java.lang.String" itemvalue="mdurl" />
            <item index="151" class="java.lang.String" itemvalue="smart-open" />
            <item index="152" class="java.lang.String" itemvalue="blis" />
            <item index="153" class="java.lang.String" itemvalue="annotated-types" />
            <item index="154" class="java.lang.String" itemvalue="scipy" />
            <item index="155" class="java.lang.String" itemvalue="typer" />
            <item index="156" class="java.lang.String" itemvalue="pytesseract" />
            <item index="157" class="java.lang.String" itemvalue="rich" />
            <item index="158" class="java.lang.String" itemvalue="weasel" />
            <item index="159" class="java.lang.String" itemvalue="opentelemetry-semantic-conventions" />
            <item index="160" class="java.lang.String" itemvalue="langcodes" />
            <item index="161" class="java.lang.String" itemvalue="pandas" />
            <item index="162" class="java.lang.String" itemvalue="google-cloud-pubsub" />
            <item index="163" class="java.lang.String" itemvalue="proto-plus" />
            <item index="164" class="java.lang.String" itemvalue="thinc" />
            <item index="165" class="java.lang.String" itemvalue="grpcio" />
            <item index="166" class="java.lang.String" itemvalue="cloudpathlib" />
            <item index="167" class="java.lang.String" itemvalue="fpdf" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="datetime.date.today" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>