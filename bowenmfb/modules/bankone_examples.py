"""
Example usage of the BankOne API client.

This file demonstrates how to use the BankOneClient for various operations.
"""

from .bankone import get_bankone_client
from .bankone_exceptions import BankOneAPIError, BankOneAuthenticationError


def example_create_customer():
    """Example: Create an individual customer."""
    client = get_bankone_client()
    
    try:
        response = client.create_customer(
            last_name="<PERSON><PERSON>",
            other_names="<PERSON>",
            city="Lagos",
            address="123 Main Street, Lagos",
            gender=0,  # 0 for Male, 1 for Female
            date_of_birth="1990-01-15",
            phone_no="***********",
            place_of_birth="Lagos",
            national_identity_no="***********",
            next_of_kin_name="<PERSON>",
            next_of_kin_phone_number="***********",
            referral_name="<PERSON>",
            referral_phone_no="***********",
            bvn="***********",
            email="<EMAIL>"
        )
        
        print(f"Customer created successfully: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to create customer: {e}")
        return None


def example_create_account():
    """Example: Create an account quickly."""
    client = get_bankone_client()
    
    try:
        response = client.create_account_quick(
            transaction_tracking_ref="TXN123456",
            account_opening_tracking_ref="ACC123456",
            product_code="101",
            customer_id="004529",  # From customer creation response
            last_name="Doe",
            other_names="John",
            bvn="***********",
            phone_no="***********",
            gender="0",
            place_of_birth="Lagos",
            date_of_birth="1990-01-15",
            address="123 Main Street, Lagos",
            account_officer_code="001",
            email="<EMAIL>"
        )
        
        print(f"Account created successfully: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to create account: {e}")
        return None


def example_get_account_balance():
    """Example: Get account balance."""
    client = get_bankone_client()
    
    try:
        response = client.get_account_by_number("**********")
        
        print(f"Account details: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to get account details: {e}")
        return None


def example_inter_bank_transfer():
    """Example: Perform inter-bank transfer."""
    client = get_bankone_client()
    
    try:
        response = client.inter_bank_transfer(
            amount=5000.0,  # Will be multiplied by 100 automatically
            payer="John Doe",
            payer_account_number="**********",
            receiver_account_number="**********",
            receiver_bank_code="076",
            narration="Transfer to friend",
            transaction_reference="TF24107924",
            token="your_api_token_here"
        )
        
        print(f"Transfer successful: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Transfer failed: {e}")
        return None


def example_local_transfer():
    """Example: Perform local/intra-bank transfer."""
    client = get_bankone_client()
    
    try:
        response = client.local_funds_transfer(
            from_account_number="**********",
            amount=1000.0,  # Will be multiplied by 100 automatically
            to_account_number="**********",
            retrieval_reference="testintra1",
            narration="Local funds transfer",
            authentication_key="c66ef21b-cf4d-420f-9c29-aee513bd6854"
        )
        
        print(f"Local transfer successful: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Local transfer failed: {e}")
        return None


def example_name_enquiry():
    """Example: Perform name enquiry."""
    client = get_bankone_client()
    
    try:
        response = client.name_enquiry(
            account_number="********",
            bank_code="678",
            token="your_api_token_here"
        )
        
        print(f"Name enquiry result: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Name enquiry failed: {e}")
        return None


def example_generate_statement():
    """Example: Generate account statement."""
    client = get_bankone_client()
    
    try:
        response = client.generate_account_statement(
            account_number="**********",
            from_date="2024-01-01",
            to_date="2024-01-31"
        )
        
        if response.get("IsSuccessful"):
            # Save the PDF statement
            pdf_data = response.get("Message")
            if pdf_data:
                client.save_pdf_statement(pdf_data, "statement.pdf")
                print("Statement saved as statement.pdf")
        
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to generate statement: {e}")
        return None


def example_transaction_status():
    """Example: Check transaction status."""
    client = get_bankone_client()
    
    try:
        # For inter-bank transaction
        response = client.inter_bank_transaction_status(
            amount=5000.0,
            retrieval_reference="TF24107924",
            transaction_date="2024-01-15",
            token="your_api_token_here"
        )
        
        print(f"Transaction status: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to check transaction status: {e}")
        return None


def example_freeze_account():
    """Example: Freeze an account."""
    client = get_bankone_client()
    
    try:
        response = client.freeze_account(
            account_no="**********",
            authentication_code="XXXXXX-XXXX-XXXX-XXXX-XXXXXXXXX",
            reference_id="********",
            reason="suspected fraud"
        )
        
        print(f"Account freeze result: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to freeze account: {e}")
        return None


if __name__ == "__main__":
    # Run examples (uncomment as needed)
    # example_create_customer()
    # example_create_account()
    # example_get_account_balance()
    # example_inter_bank_transfer()
    # example_local_transfer()
    # example_name_enquiry()
    # example_generate_statement()
    # example_transaction_status()
    # example_freeze_account()
    pass
