"""
Custom exceptions for BankOne API operations.
"""


class BankOneAPIError(Exception):
    """Base exception for BankOne API errors."""
    pass


class BankOneAuthenticationError(BankOneAPIError):
    """Raised when authentication fails."""
    pass


class BankOneValidationError(BankOneAPIError):
    """Raised when request validation fails."""
    pass


class BankOneNetworkError(BankOneAPIError):
    """Raised when network/connection issues occur."""
    pass


class BankOneTransactionError(BankOneAPIError):
    """Raised when transaction operations fail."""
    pass


class BankOneAccountError(BankOneAPIError):
    """Raised when account operations fail."""
    pass


class BankOneCustomerError(BankOneAPIError):
    """Raised when customer operations fail."""
    pass
