# BankOne API Client Module

This module provides a comprehensive Python client for interacting with BankOne core banking APIs. It includes all the major banking operations such as customer management, account operations, transfers, and transaction queries.

## Features

- **Complete API Coverage**: All 16 BankOne API endpoints implemented
- **Automatic Amount Conversion**: Amounts are automatically multiplied by 100 as required by BankOne
- **Error Handling**: Comprehensive exception handling with custom exception types
- **Logging Integration**: Full request/response logging using Django's logging framework
- **Configuration Management**: Uses Django models for storing API credentials
- **PDF Support**: Built-in support for decoding and saving PDF account statements
- **Type Hints**: Full type annotations for better IDE support and code maintainability

## Files

- `bankone.py` - Main BankOne API client class
- `bankone_exceptions.py` - Custom exception classes
- `bankone_examples.py` - Usage examples for all API methods
- `test_bankone.py` - Basic tests for the module
- `README_BANKONE.md` - This documentation file

## Setup

1. **Configure Bank Settings**: Add your BankOne credentials to the `BankConstantTable` model:
   ```python
   from account.models import BankConstantTable
   
   bank_config = BankConstantTable.objects.create(
       name="BowenMFB",
       auth_token="your_bankone_auth_token",
       institution_code="your_institution_code"
   )
   ```

2. **Install Dependencies**: Ensure `requests` is installed:
   ```bash
   pip install requests
   ```

## Basic Usage

```python
from bowenmfb.modules.bankone import get_bankone_client
from bowenmfb.modules.bankone_exceptions import BankOneAPIError

# Get a configured client
client = get_bankone_client()

try:
    # Create a customer
    customer = client.create_customer(
        last_name="Doe",
        other_names="John",
        city="Lagos",
        address="123 Main Street",
        gender=0,  # 0=Male, 1=Female
        date_of_birth="1990-01-15",
        phone_no="***********",
        place_of_birth="Lagos",
        national_identity_no="***********",
        next_of_kin_name="Jane Doe",
        next_of_kin_phone_number="***********",
        referral_name="Agent Smith",
        referral_phone_no="***********",
        bvn="***********",
        email="<EMAIL>"
    )
    
    print(f"Customer created: {customer['CustomerID']}")
    
except BankOneAPIError as e:
    print(f"Error: {e}")
```

## API Methods

### Customer Management
- `create_customer()` - Create individual customer
- `create_organization_customer()` - Create corporate customer
- `get_customer_by_account_number()` - Get customer by account number

### Account Management
- `create_account_quick()` - Quick account creation
- `get_account_by_number()` - Get account details and balances
- `get_accounts_by_customer_id()` - Get all customer accounts
- `generate_account_statement()` - Generate PDF account statement

### Account Control
- `freeze_account()` - Freeze an account
- `unfreeze_account()` - Unfreeze an account

### Validation & Enquiry
- `get_bvn_details()` - Validate BVN and get customer details
- `name_enquiry()` - Verify account name
- `get_commercial_banks()` - Get list of banks

### Transfers
- `inter_bank_transfer()` - Transfer to other banks
- `local_funds_transfer()` - Transfer within same bank

### Transaction Management
- `inter_bank_transaction_status()` - Check inter-bank transaction status
- `local_transaction_status()` - Check local transaction status
- `transaction_reversal()` - Reverse a transaction

### Utilities
- `decode_pdf_statement()` - Decode base64 PDF to bytes
- `save_pdf_statement()` - Save PDF statement to file

## Error Handling

The module provides specific exception types:

```python
from bowenmfb.modules.bankone_exceptions import (
    BankOneAPIError,           # Base exception
    BankOneAuthenticationError, # Auth failures
    BankOneValidationError,    # Validation errors
    BankOneNetworkError,       # Network issues
    BankOneTransactionError,   # Transaction failures
    BankOneAccountError,       # Account operation failures
    BankOneCustomerError       # Customer operation failures
)
```

## Amount Handling

**Important**: All monetary amounts are automatically multiplied by 100 before sending to BankOne API (converting from Naira to Kobo). You should pass amounts in Naira:

```python
# Pass 5000.0 (5000 Naira)
# Will be sent as "500000" (500000 Kobo) to BankOne
client.inter_bank_transfer(amount=5000.0, ...)
```

## Configuration

The client automatically loads configuration from the `BankConstantTable` model. Ensure the following fields are set:

- `auth_token` - Your BankOne authentication token
- `institution_code` - Your institution code

## Testing

Run the basic tests:

```bash
python manage.py shell < bowenmfb/modules/test_bankone.py
```

## Examples

See `bankone_examples.py` for comprehensive usage examples of all API methods.

## Logging

All API requests and responses are logged using Django's logging framework. Check your log files for detailed request/response information.

## Security Notes

- Store API tokens securely in the database
- Use environment variables for sensitive configuration
- Enable request logging only in development environments
- Implement proper error handling in production code

## Support

For issues or questions about this module, refer to the BankOne API documentation or contact the development team.
